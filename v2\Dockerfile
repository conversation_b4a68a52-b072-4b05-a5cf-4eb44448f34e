# ===========================================
# 🐳 TOKENTRACKER V2 DOCKERFILE
# ===========================================
# Multi-stage build following DEPLOYMENT_SCALING.md guidelines

# 🏗️ Base stage with Python and system dependencies
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# 📦 Dependencies stage
FROM base as deps

# Copy requirements first for better caching
COPY requirements-simple.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-simple.txt

# 🧪 Development stage
FROM deps as development

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest \
    pytest-asyncio \
    pytest-cov \
    black \
    flake8 \
    mypy

# Copy source code
COPY . .

# Create non-root user for development
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 3000

# Development command
CMD ["python", "-m", "uvicorn", "src.app:app", "--host", "0.0.0.0", "--port", "3000", "--reload"]

# 🚀 Production stage - Phase 3 Optimized
FROM deps as production

# Set production build arguments
ARG BUILD_DATE
ARG VERSION
ARG VCS_REF

# Add production labels
LABEL maintainer="TokenTracker Team" \
      version="${VERSION}" \
      description="TokenTracker V2 - Advanced Token Analysis Platform" \
      build-date="${BUILD_DATE}" \
      vcs-ref="${VCS_REF}"

# Set production environment variables
ENV NODE_ENV=production \
    PYTHONPATH=/app/src \
    PORT=8000

# Install production optimizations
RUN pip install --no-cache-dir \
    uvloop \
    httptools \
    gunicorn

# Copy only necessary files
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY .env.example .env.example

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/data /app/cache /app/tmp && \
    chown -R appuser:appuser /app

# Copy and set up health check script
COPY scripts/docker-healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Switch to non-root user
USER appuser

# Health check with comprehensive monitoring endpoints
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Expose port (changed to 8000 for production)
EXPOSE 8000

# Production command with optimizations
CMD ["python", "-m", "uvicorn", "src.main:app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "4", \
     "--worker-class", "uvicorn.workers.UvicornWorker", \
     "--loop", "uvloop", \
     "--http", "httptools", \
     "--access-log", \
     "--log-level", "info"]

# 🧪 Testing stage
FROM development as testing

# Copy test files
COPY tests/ ./tests/

# Run tests
RUN python -m pytest tests/ -v --cov=src --cov-report=term-missing

# 📊 Monitoring stage (for metrics collection)
FROM production as monitoring

# Install monitoring tools
USER root
RUN pip install --no-cache-dir \
    prometheus-client \
    psutil

USER appuser

# Expose metrics port
EXPOSE 9090

# Command with metrics
CMD ["python", "-m", "uvicorn", "src.app:app", "--host", "0.0.0.0", "--port", "3000", "--workers", "4"]
